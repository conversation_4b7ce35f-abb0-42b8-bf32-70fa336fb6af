import NumberInput from "@/components/NumberInput";
import { ParentAnswerFieldProps } from "@/components/types";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const ParentAnswerField = ({ logicIndex, conditionIndex, element, operators, form }: ParentAnswerFieldProps) => {
  const isChoiceElement = ["Single Choice", "Multiple Choice", "Dropdown"].includes(element);

  const currentOperator = form.watch(`logics.${logicIndex}.if.${conditionIndex}.operator`);

  // Range inputs for "is in range" operator
  if (currentOperator === "is in range") {
    return (
      <div className="flex gap-2">
        <FormField
          control={form.control}
          name={`logics.${logicIndex}.if.${conditionIndex}.parentAnswerMinimumLimit`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <NumberInput
                  value={field.value}
                  placeholder="Min Number"
                  onChange={field.onChange} // Explicit onChange
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={`logics.${logicIndex}.if.${conditionIndex}.parentAnswerMaximumLimit`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <NumberInput
                  value={field.value} // Explicit value binding
                  placeholder="Max Number"
                  onChange={field.onChange} // Explicit onChange
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    );
  }

  // Single answer field for other operators
  return (
    <FormField
      control={form.control}
      name={`logics.${logicIndex}.if.${conditionIndex}.parentAnswer`}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            {isChoiceElement ? (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Select option" />
                </SelectTrigger>
                <SelectContent>
                  {operators.map(option => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <NumberInput {...field} placeholder="Enter value" onChange={field.onChange} />
            )}
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export default ParentAnswerField;
