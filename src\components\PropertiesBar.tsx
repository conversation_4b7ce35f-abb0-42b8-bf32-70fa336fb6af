import { fredoka } from "@/app/fonts";
import plusIcon from "@/assets/icons/plus-green.svg";
import BasicProperties from "@/components/BasicProperties";
import AudioRecordingProperties from "@/components/basics/Audio/AudioRecordingProperties";
import CameraProperties from "@/components/basics/Camera/CameraProperties";
import DateProperties from "@/components/basics/Date/DateProperties";
import DropdownProperties from "@/components/basics/Dropdown/DropdownProperties";
import FileProperties from "@/components/basics/File/FileProperties";
import MultipleChoiceProperties from "@/components/basics/MultipleChoice/MultipleChoiceProperties";
import NumberProperties from "@/components/basics/Number/NumberProperties";
import ParagraphProperties from "@/components/basics/Paragraph/ParagraphProperties";
import PhoneNumberProperties from "@/components/basics/PhoneNumber/PhoneNumberProperties";
import RatingProperties from "@/components/basics/Rating/RatingProperties";
import ReadOnlyProperties from "@/components/basics/ReadOnly/ReadOnlyProperties";
import ShortAnswerProperties from "@/components/basics/ShortAnswer/ShortAnswerProperties";
import SingleChoiceProperties from "@/components/basics/SingleChoice/SingleChoiceProperties";
import TimeProperties from "@/components/basics/Time/TimeProperties";
import ScreenProperties from "@/components/layouts/Screen/ScreenProperties";
import SectionProperties from "@/components/layouts/Section/SectionProperties";
import { ElementProperties, ElementType, FormElement } from "@/components/types";
import { Button } from "@/components/ui/button";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { openLogicModal } from "@/lib/redux/slices/dialogSlice";
import { addFormElement, removeFormElement, updateSelectedFormBuilderItem } from "@/lib/redux/slices/formSlice";
import { findFormElementSectionId, generateId, transformElementType } from "@/lib/utils";
import Image from "next/image";
import { useState } from "react";
import { VscTriangleRight } from "react-icons/vsc";

export const FIELD_PROPERTIES: Record<ElementProperties, React.FC> = {
  AudioRecordingProperties,
  CameraProperties,
  DateProperties,
  DropdownProperties,
  FileProperties,
  MultipleChoiceProperties,
  NumberProperties,
  ParagraphProperties,
  PhoneNumberProperties,
  RatingProperties,
  ReadOnlyProperties,
  SectionProperties,
  SingleChoiceProperties,
  ShortAnswerProperties,
  TimeProperties,
};
const PropertiesBar = () => {
  const dispatch = useAppDispatch();
  const [isLogicOpen, setIsLogicOpen] = useState<boolean>(false);
  const [isGeneralOpen, setIsGeneralOpen] = useState<boolean>(true);
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem);
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const elementType = selectedFormBuilderItem?.type;
  const elementName = elementType?.endsWith("Field") ? elementType.split("Field") : elementType?.split("Layout");
  const component = elementName?.join("" + "Properties") || "";
  const Component = FIELD_PROPERTIES[component as ElementProperties];

  const handleDeleteField = () => {
    dispatch(removeFormElement({ screenId, sectionId, elementId: selectedFormBuilderItem?.id ?? "" }));
    dispatch(updateSelectedFormBuilderItem({ id: screenId, type: "ScreenLayout" }));
  };

  const handleDuplicateField = () => {
    const currentScreenIndex = formScreens.findIndex(screen => screen.id === screenId);
    const currentScreen = formScreens[currentScreenIndex];
    const currentSectionIndex = currentScreen.sections.findIndex(section => section.id === sectionId);
    const currentSection = currentScreen.sections[currentSectionIndex];
    const elementIndex = currentSection.elements.findIndex(element => element.id === selectedFormBuilderItem?.id);
    if (elementIndex === -1) {
      throw new Error("Element not found");
    }

    const duplicatedElement = {
      ...selectedFormBuilderItem,
      id: `${transformElementType(selectedFormBuilderItem?.type as ElementType)}_${generateId()}`,
    } as FormElement;

    dispatch(addFormElement({ screenId, sectionId, elementIndex: elementIndex + 1, element: duplicatedElement }));
  };

  const handleAddLogic = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    dispatch(openLogicModal());
  };

  const hasHint = component !== "ReadOnlyProperties";
  const hasTag = component !== "ReadOnlyProperties";
  const hasTooltip = component !== "ReadOnlyProperties";
  const hasRequired = component !== "ReadOnlyProperties";
  const propertiesWithValidate = ["ShortAnswerProperties", "NumberProperties", "PhoneNumberProperties", "DateProperties"];
  const hasValidate = propertiesWithValidate.includes(component);

  return (
    <aside className="fixed right-0 top-20 mb-4 h-[calc(100vh-5rem)] w-[280px] overflow-y-auto bg-white p-4">
      <p className={`${fredoka.className} mb-4 text-lg font-semibold`}>Properties</p>
      {selectedFormBuilderItem?.type === "ScreenLayout" ? (
        <ScreenProperties />
      ) : selectedFormBuilderItem?.type === "SectionLayout" ? (
        <SectionProperties />
      ) : (
        <div>
          <Button
            variant="ghost"
            className={`${fredoka.className} cursor-pointer items-center gap-1 p-0 text-lg font-semibold hover:bg-transparent`}
            onClick={() => setIsLogicOpen(!isLogicOpen)}
          >
            <VscTriangleRight className={`${isLogicOpen ? "rotate-90" : "rotate-0"} transition duration-200 ease-in-out`} /> Logic
          </Button>
          {isLogicOpen && (
            <Button variant="ghost" className="flex cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent" onClick={handleAddLogic}>
              <Image src={plusIcon} alt="Plus Icon" width={14} height={14} />
              Add Logic
            </Button>
          )}
          <Button
            variant="ghost"
            className={`${fredoka.className} flex cursor-pointer items-center gap-1 p-0 text-lg font-semibold hover:bg-transparent`}
            onClick={() => setIsGeneralOpen(!isGeneralOpen)}
          >
            <VscTriangleRight className={`${isGeneralOpen ? "rotate-90" : "rotate-0"} transition duration-200 ease-in-out`} /> General
          </Button>
          {isGeneralOpen && (
            <>
              <div className="space-y-6">
                <BasicProperties hasHint={hasHint} hasTooltip={hasTooltip} hasRequired={hasRequired} hasValidate={hasValidate} hasTag={hasTag} />
                {component && <Component />}
              </div>
              <div className="mt-8 flex flex-col gap-2">
                <Button className={`h-[3.4rem] w-full ${fredoka.className} text-lg font-semibold`} variant="outline" onClick={handleDuplicateField}>
                  Duplicate Field
                </Button>
                <Button className={`h-[3.4rem] w-full ${fredoka.className} text-lg font-semibold`} variant="outline-red" onClick={handleDeleteField}>
                  Delete Field
                </Button>
              </div>
            </>
          )}
        </div>
      )}
    </aside>
  );
};

export default PropertiesBar;
