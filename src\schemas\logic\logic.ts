import * as z from "zod";
const conditionSchema = z.object({
  id: z.string(),
  parentQuestion: z.string().optional(),
  parentAnswer: z.string().optional(),
  parentAnswerMinimumLimit: z.string().optional(),
  parentAnswerMaximumLimit: z.string().optional(),
  operator: z.string().optional(),
  logic: z.string().nullable(),
});

const logicSchema = z.object({
  id: z.string(),
  if: z.array(conditionSchema).optional(),
  then: z.string().optional(),
});

export const elementLogicSchema = z.object({
  logics: z.array(logicSchema).min(1, "At least one logic is required"),
});
